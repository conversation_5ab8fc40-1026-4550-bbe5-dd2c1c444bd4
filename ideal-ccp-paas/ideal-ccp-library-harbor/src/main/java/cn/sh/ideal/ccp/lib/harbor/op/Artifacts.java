package cn.sh.ideal.ccp.lib.harbor.op;


import cn.sh.ideal.ccp.lib.harbor.HarborResponse;
import cn.sh.ideal.ccp.lib.harbor.model.Artifact;
import cn.sh.ideal.ccp.lib.harbor.model.ArtifactListFilter;
import cn.sh.ideal.ccp.lib.harbor.op.handler.ArtifactHandler;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface Artifacts {

  List<Artifact> list();

  List<Artifact> list(ArtifactListFilter filter);

  ArtifactHandler withReference(String reference);

  Integer count();

  HarborResponse copy(String from);
}
