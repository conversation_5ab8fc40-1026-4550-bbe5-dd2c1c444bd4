/*
 * KS API
 * KubeSphere OpenAPI
 *
 * The version of the OpenAPI document: v4.1.1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package cn.sh.ideal.ccp.lib.kubeSphere.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * Namespace provides a scope for Names. Use of multiple namespaces is optional.
 */
@JsonPropertyOrder({
  V2Application.JSON_PROPERTY_API_VERSION,
  V2Application.JSON_PROPERTY_KIND,
  V2Application.JSON_PROPERTY_METADATA,
  V2Application.JSON_PROPERTY_SPEC,
  V2Application.JSON_PROPERTY_STATUS
})
@JsonTypeName("v1.Application")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-05-07T14:59:55.982671800+08:00[GMT+08:00]", comments = "Generator version: 7.13.0")
public class V2Application {
  public static final String JSON_PROPERTY_API_VERSION = "apiVersion";
  @javax.annotation.Nonnull
  private String apiVersion = "application.kubesphere.io/v2";

  public static final String JSON_PROPERTY_KIND = "kind";
  @javax.annotation.Nonnull
  private String kind = "Application";

  public static final String JSON_PROPERTY_METADATA = "metadata";
  @javax.annotation.Nonnull
  private V1ObjectMeta metadata;

  public static final String JSON_PROPERTY_SPEC = "spec";
  @javax.annotation.Nullable
  private V2ApplicationSpec spec;

  public static final String JSON_PROPERTY_STATUS = "status";
  @javax.annotation.Nullable
  private V2ApplicationStatus status;

  public V2Application() {
  }

  public V2Application apiVersion(@javax.annotation.Nullable String apiVersion) {

    this.apiVersion = apiVersion;
    return this;
  }

  /**
   * APIVersion defines the versioned schema of this representation of an object. Servers should convert recognized schemas to the latest internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
   * @return apiVersion
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_API_VERSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getApiVersion() {
    return apiVersion;
  }


  @JsonProperty(JSON_PROPERTY_API_VERSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setApiVersion(@javax.annotation.Nullable String apiVersion) {
    this.apiVersion = apiVersion;
  }

  public V2Application kind(@javax.annotation.Nullable String kind) {

    this.kind = kind;
    return this;
  }

  /**
   * Kind is a string value representing the REST resource this object represents. Servers may infer this from the endpoint the client submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
   * @return kind
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_KIND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getKind() {
    return kind;
  }


  @JsonProperty(JSON_PROPERTY_KIND)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setKind(@javax.annotation.Nullable String kind) {
    this.kind = kind;
  }

  public V2Application metadata(@javax.annotation.Nullable V1ObjectMeta metadata) {

    this.metadata = metadata;
    return this;
  }

  /**
   * Get metadata
   * @return metadata
   */
  @javax.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public V1ObjectMeta getMetadata() {
    return metadata;
  }


  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMetadata(@javax.annotation.Nullable V1ObjectMeta metadata) {
    this.metadata = metadata;
  }

  public V2Application spec(@javax.annotation.Nullable V2ApplicationSpec spec) {

    this.spec = spec;
    return this;
  }

  /**
   * Get spec
   * @return spec
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SPEC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public V2ApplicationSpec getSpec() {
    return spec;
  }


  @JsonProperty(JSON_PROPERTY_SPEC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSpec(@javax.annotation.Nullable V2ApplicationSpec spec) {
    this.spec = spec;
  }

  public V2Application status(@javax.annotation.Nullable V2ApplicationStatus status) {

    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
   */
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public V2ApplicationStatus getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(@javax.annotation.Nullable V2ApplicationStatus status) {
    this.status = status;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    V2Application v1Namespace = (V2Application) o;
    return Objects.equals(this.apiVersion, v1Namespace.apiVersion) &&
        Objects.equals(this.kind, v1Namespace.kind) &&
        Objects.equals(this.metadata, v1Namespace.metadata) &&
        Objects.equals(this.spec, v1Namespace.spec) &&
        Objects.equals(this.status, v1Namespace.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(apiVersion, kind, metadata, spec, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class V1Namespace {\n");
    sb.append("    apiVersion: ").append(toIndentedString(apiVersion)).append("\n");
    sb.append("    kind: ").append(toIndentedString(kind)).append("\n");
    sb.append("    metadata: ").append(toIndentedString(metadata)).append("\n");
    sb.append("    spec: ").append(toIndentedString(spec)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

