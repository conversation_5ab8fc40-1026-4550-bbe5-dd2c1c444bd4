package cn.sh.ideal.ccp.container.pojo;

import lombok.Getter;
import lombok.Setter;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ServiceInfo {
  /**
   * 内部IP
   */
  @NotNull
  private String clusterIp;
  /**
   * 外部IP
   */
  @NotNull
  private String serviceIp;
  /**
   * 服务端口
   */
  private List<ServicePortInfo> ports = new ArrayList<>(3);


}
