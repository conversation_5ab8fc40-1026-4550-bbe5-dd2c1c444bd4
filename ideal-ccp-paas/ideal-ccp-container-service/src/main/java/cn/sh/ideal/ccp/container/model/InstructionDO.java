package cn.sh.ideal.ccp.container.model;

import cn.sh.ideal.ccp.container.args.InstructionCreateArgs;
import cn.sh.ideal.ccp.container.args.InstructionModifyArgs;
import cn.sh.ideal.ccp.container.pojo.InstructionInfo;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Comment;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;


@Getter
@Setter
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = InstructionDO.ENTITY_NAME)
@Comment("prometheus指令")
public class InstructionDO implements Serializable {
  public static final String ENTITY_NAME = "ccp_prometheus_instruction";

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Comment("主键")
  @Column(nullable = false, name = "id_")
  private Long id = null;

  /**
   * 名称
   */
  @Column(name = "name", length = 64)
  private String name;
  /**
   * 指令
   */
  @Column(name = "instruction", columnDefinition = "text")
  private String instruction;

  @CreatedDate
  @Comment("创建时间戳")
  @Column(nullable = false, name = "created_time_")
  private long createdTime = 0;

  @LastModifiedDate
  @Comment("更新时间")
  @Column(nullable = false, name = "updated_time_")
  private long updatedTime = 0;

  public static InstructionDO of(InstructionCreateArgs args) {
    InstructionDO instructionDO = new InstructionDO();
    instructionDO.setName(args.getName());
    instructionDO.setInstruction(args.getInstruction());
    return instructionDO;
  }

  public InstructionInfo toEntity() {
    InstructionInfo info = new InstructionInfo();
    info.setId(id);
    info.setName(name);
    info.setInstruction(instruction);
    info.setCreatedTime(createdTime);
    info.setUpdatedTime(updatedTime);
    return info;
  }

  public void update(InstructionModifyArgs args) {
    this.setInstruction(args.getInstruction());
    this.setName(args.getName());
  }

}
