package cn.sh.ideal.ccp.container.model;

import cn.sh.ideal.ccp.container.build.KubeSphereClient;
import cn.sh.ideal.ccp.container.configure.ContainerProperties;
import cn.sh.ideal.ccp.lib.kubeSphere.model.*;
import jakarta.annotation.Nonnull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class KubeSphereAppsApi {

  private final KubeSphereClient kubeSphereClient;
  private final ContainerProperties properties;


  /**
   * 查看应用商店应用列表
   */
  public V2ApplicationList queryApps() {
    return kubeSphereClient.getDefaultApi()
      .listAppsWithResponseSpec()
      .bodyToMono(V2ApplicationList.class)
      .block();
  }

  /**
   * 查询应用商店应用信息
   * @param name
   * @return
   */
  public V2Application queryApp(@Nonnull String name) {
    return kubeSphereClient.getDefaultApi()
      .getApp(name)
      .bodyToMono(V2Application.class)
      .block();
  }



  /**
   * 查询应用分类
   */
  public V2CategoryList queryCategories() {
    return kubeSphereClient.getDefaultApi()
      .listCategoriesWithResponseSpec()
      .bodyToMono(V2CategoryList.class)
      .block();
  }

  /**
   * 保存应用分类
   */
  public V2Category saveCategory(@Nonnull V2Category category) {
    return kubeSphereClient.getDefaultApi()
      .createOrUpdateCategoryWithResponseSpec(category)
      .bodyToMono(V2Category.class)
      .block();
  }

  /**
   * 删除应用分类
   */
  public void deleteCategory(@Nonnull String name) {
    kubeSphereClient.getDefaultApi()
      .deleteCategory(name)
      .block();
  }



  /**
   * 查询应用仓库列表
   */
  public V2RepoList queryRepos() {
    return kubeSphereClient.getDefaultApi()
      .listReposWithResponseSpec()
      .bodyToMono(V2RepoList.class)
      .block();
  }

  /**
   * 校验应用仓库信息的正确性
   */
  public boolean validateRepo(@Nonnull V2Repo repo) {
    ValidateResult result =kubeSphereClient.getDefaultApi()
        .createOrUpdateRepoWithResponseSpec(repo, true)
        .bodyToMono(ValidateResult.class)
        .block();
    return result != null && result.ok();
  }

  /**
   * 添加应用仓库
   * @param repo 应用仓库配置信息
   */
  public V2Repo createRepo(@Nonnull V2Repo repo) {
    return kubeSphereClient.getDefaultApi()
        .createOrUpdateRepoWithResponseSpec(repo, false)
        .bodyToMono(V2Repo.class)
        .block();
  }

  /**
   * 更新应用仓库
   */
  public V2Repo updateRepo(@Nonnull V2Repo repo) {
    return kubeSphereClient.getDefaultApi()
        .createOrUpdateRepo_0WithResponseSpec(repo)
        .bodyToMono(V2Repo.class)
        .block();
  }

  /**
   * 删除应用仓库
   */
  public void deleteRepo(@Nonnull String name) {
    kubeSphereClient.getDefaultApi()
        .deleteRepo(name)
        .block();
  }










}
