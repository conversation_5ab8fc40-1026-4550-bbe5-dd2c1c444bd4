package cn.sh.ideal.ccp.container.model;


import cn.sh.ideal.ccp.api.container.NamespaceApi;
import io.fabric8.kubernetes.api.model.NamespaceBuilder;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class KubeNamespaceApi {

  private final KubernetesClient client;


  /**
   * 创建命名空间, 如果已经存在则跳过
   *
   * @param namespace 命名空间
   */
  public void createIfNotExist(@NotNull String namespace) {
    if ("default".equals(namespace)) {
      return;
    }
    var namespaceDo = client.namespaces()
      .withName(namespace)
      .get();
    if (namespaceDo == null) {
      log.info("create Namespace {}", namespace);
      client.namespaces()
          .resource(new NamespaceBuilder()
              .withNewMetadata()
              .withName(namespace)
              .endMetadata()
              .build())
          .create();
    }
  }


  /**
   * 删除命名空间
   * @param namespace 命名空间名称
   */
  public void delete(@NotNull String namespace) {
    if (!client.apps().deployments().inNamespace(namespace)
      .list()
      .getItems()
      .isEmpty()) {
      throw new IllegalArgumentException("用户空间下存在工作负载，无法删除");
    }

    log.info("delete Namespace {}", namespace);
    client.services().inNamespace(namespace).delete();
    client.configMaps().inNamespace(namespace).delete();
    client.secrets().inNamespace(namespace).delete();
    client.namespaces().withName(namespace).delete();
  }

}
